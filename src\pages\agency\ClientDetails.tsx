import { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  ArrowLeft,
  Building2,
  User,
  Mail,
  Phone,
  MapPin,
  FileText,
  Briefcase,
  Edit,
  Trash2,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { useToast } from '@/core';
import { getAgencyClient, deleteAgencyClient } from '@/features/agency-clients/api/clientService';
import { ClientModal } from '@/features/agency-clients/components';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import type { AgencyClientWithContacts } from '@/features/agency-clients/types';

const ClientDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [client, setClient] = useState<AgencyClientWithContacts | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchClient = async () => {
      if (!id) {
        setError('Client ID not provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const clientData = await getAgencyClient(id);

        if (!clientData) {
          setError('Client not found');
          return;
        }

        setClient(clientData);
      } catch (err) {
        console.error('Error fetching client:', err);
        setError('Failed to load client details');
        toast({
          title: 'Error',
          description: 'Failed to load client details. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchClient();
  }, [id]);

  const handleEdit = () => {
    setShowEditModal(true);
  };

  const handleDelete = () => {
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!client || !id) return;

    try {
      setIsDeleting(true);
      await deleteAgencyClient(id);

      toast({
        title: 'Success',
        description: 'Client deleted successfully.',
      });

      navigate('/agency/clients');
    } catch (error) {
      console.error('Error deleting client:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete client. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  const handleEditSuccess = () => {
    // Refresh client data after edit
    if (id) {
      getAgencyClient(id).then(setClient);
    }
  };

  const getClientIcon = (type: string) => {
    return type === 'company' ?
      <Building2 className="h-5 w-5 text-blue-500" /> :
      <User className="h-5 w-5 text-green-500" />;
  };

  if (loading) {
    return (
      <DashboardLayout userType="agency">
        <div className="flex items-center justify-center h-[calc(100vh-200px)]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">Loading client details...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !client) {
    return (
      <DashboardLayout userType="agency">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error || 'Client not found'}</AlertDescription>
        </Alert>
        <Button onClick={() => navigate('/agency/clients')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Clients
        </Button>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout userType="agency">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" onClick={() => navigate('/agency/clients')}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div className="flex items-center gap-3">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">{client.name}</h1>
                <p className="text-muted-foreground">Client ID: {client.id}</p>
              </div>
              <Badge variant={client.type === 'company' ? 'default' : 'secondary'}>
                {client.type === 'company' ? 'Company' : 'Individual'}
              </Badge>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" onClick={handleDelete} className="text-red-600 hover:text-red-700">
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column - Client Information (2/3 width) */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Client Information</CardTitle>
              </CardHeader>
              <CardContent className="p-0">

                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y sm:divide-x divide-gray-100">
                                  <div className="p-6">
                                    <p className="text-sm text-gray-500 mb-1">Email</p>
                                    <div className="flex items-center gap-2">
                                      <Mail className="h-4 w-4 text-gray-400" />
                                      <span>{client.email}</span>
                                    </div>
                                  </div>
                                  <div className="p-6">
                                    <p className="text-sm text-gray-500 mb-1">Phone</p>
                                    <div className="flex items-center gap-2">
                                      <Phone className="h-4 w-4 text-gray-400" />
                                      <span>{client.phone}</span>
                                    </div>
                                  </div>
                                </div>

                                <div className="grid grid-cols-1 sm:grid-cols-2 gap-0 divide-y sm:divide-y-0 sm:divide-x divide-gray-100 border-t">
                                  <div className="p-6">
                                    <p className="text-sm text-gray-500 mb-1">Address</p>
                                    <div className="flex items-start gap-2">
                                      <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                                      <span>{client.address}</span>
                                    </div>
                                  </div>
                                  <div className="p-6">
                                    <p className="text-sm text-gray-500 mb-1">VAT</p>
                                    <div className="flex items-center gap-2">
                                      <FileText className="h-4 w-4 text-gray-400" />
                                      <span>{client.vat_number}</span>
                                    </div>
                                  </div>
                                </div>

                {/* Notes */}
                
              </CardContent>
            </Card>
            {client.notes && (
                  <Card className="overflow-hidden">
                            <CardHeader className="border-b bg-transparent">
                              <CardTitle>Description</CardTitle>
                            </CardHeader>
                            <CardContent className="p-6">
                              <h4 className="text-sm font-medium text-gray-500">Notes</h4>
                              <p className="text-gray-700 whitespace-pre-wrap">{client.notes}</p>
                            </CardContent>
                          </Card>
                )}
            
          </div>

          {/* Right column - Contacts (1/3 width) */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  Contacts
                  {client.contacts && client.contacts.length > 0 && (
                    <Badge variant="outline">
                      {client.contacts.length}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {client.type === 'company' ? (
                  client.contacts && client.contacts.length > 0 ? (
                    <div className="space-y-4">
                      {client.contacts.map((contact) => (
                        <div key={contact.id} className="p-3 border rounded-lg">
                          <div className="flex items-center gap-2 mb-2">
                            <User className="h-4 w-4 text-gray-500" />
                            <span className="font-medium">{contact.name}</span>
                            {contact.is_primary && (
                              <Badge variant="default" className="text-xs">
                                Primary
                              </Badge>
                            )}
                          </div>
                          <div className="space-y-1 text-sm text-gray-600">
                            {contact.position && (
                              <div className="flex items-center gap-2">
                                <Briefcase className="h-3 w-3" />
                                <span>{contact.position}</span>
                              </div>
                            )}
                            {contact.email && (
                              <div className="flex items-center gap-2">
                                <Mail className="h-3 w-3" />
                                <span>{contact.email}</span>
                              </div>
                            )}
                            {contact.phone && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-3 w-3" />
                                <span>{contact.phone}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <User className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      <p>No contacts added yet</p>
                    </div>
                  )
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <p>Individual clients don't have separate contacts</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Client Metadata */}
            <Card>
              <CardContent className="pt-4">
                <div className="space-y-2 text-sm text-gray-500">
                  <div className="flex items-center justify-between">
                    <span>Created:</span>
                    <span>{new Date(client.created_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Last Updated:</span>
                    <span>{new Date(client.updated_at).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Edit Modal */}
      <ClientModal
        open={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSuccess={handleEditSuccess}
        client={client}
        mode="edit"
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDeleteConfirm}
        title="Delete Client"
        description={`Are you sure you want to delete "${client?.name}"? This will also delete all associated contacts and cannot be undone.`}
        itemType="client"
        isDeleting={isDeleting}
      />
    </DashboardLayout>
  );
};

export default ClientDetails;
