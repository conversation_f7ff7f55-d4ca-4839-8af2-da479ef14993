import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/core';
import { supabase } from '@/core/api/supabase';
import { useAuth } from '@/contexts/AuthContext';

// Import form section components
import { BasicInformation, ContactManagement } from './form-sections';

// Import API services
import { createAgencyClient, updateAgencyClient } from '../api/clientService';
import type { AgencyClientWithContacts, CreateAgencyClientContactData } from '../types';

interface ClientModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  client?: AgencyClientWithContacts | null; // For editing
  mode: 'create' | 'edit';
}

const ClientModal = ({ open, onClose, onSuccess, client, mode }: ClientModalProps) => {
  const { profile } = useAuth();
  const { toast } = useToast();
  
  // Form state
  const [name, setName] = useState('');
  const [type, setType] = useState<'company' | 'person'>('company');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const [vatNumber, setVatNumber] = useState('');
  const [notes, setNotes] = useState('');
  const [contacts, setContacts] = useState<CreateAgencyClientContactData[]>([]);
  const [loading, setLoading] = useState(false);
  const [agencyEntityId, setAgencyEntityId] = useState<string | null>(null);

  // Get agency entity ID
  useEffect(() => {
    const getAgencyEntityId = async () => {
      if (!profile?.id) return;

      try {
        const { data: agencyEntities, error } = await supabase
          .from('entity_users')
          .select('entity_id')
          .eq('user_id', profile.id);

        if (error) {
          console.error('Error fetching agency entities:', error);
          return;
        }

        if (agencyEntities && agencyEntities.length > 0) {
          setAgencyEntityId(agencyEntities[0].entity_id);
        }
      } catch (error) {
        console.error('Exception fetching agency entity:', error);
      }
    };

    if (open) {
      getAgencyEntityId();
    }
  }, [profile?.id, open]);

  // Initialize form with client data for editing
  useEffect(() => {
    if (mode === 'edit' && client) {
      setName(client.name);
      setType(client.type);
      setEmail(client.email || '');
      setPhone(client.phone || '');
      setAddress(client.address || '');
      setVatNumber(client.vat_number || '');
      setNotes(client.notes || '');
      
      // Convert existing contacts to the form format
      if (client.contacts && Array.isArray(client.contacts)) {
        const formattedContacts = client.contacts.map(contact => ({
          name: contact.name,
          email: contact.email || '',
          phone: contact.phone || '',
          position: contact.position || '',
          is_primary: contact.is_primary
        }));
        setContacts(formattedContacts);
      }
    } else {
      // Reset form for create mode
      resetForm();
    }
  }, [mode, client, open]);

  const resetForm = () => {
    setName('');
    setType('company');
    setEmail('');
    setPhone('');
    setAddress('');
    setVatNumber('');
    setNotes('');
    setContacts([]);
  };

  const validateForm = () => {
    if (!name.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Client name is required.',
        variant: 'destructive',
      });
      return false;
    }

    if (!agencyEntityId) {
      toast({
        title: 'Error',
        description: 'Unable to determine agency. Please try again.',
        variant: 'destructive',
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const clientData = {
        agency_entity_id: agencyEntityId!,
        name: name.trim(),
        type,
        email: email.trim() || undefined,
        phone: phone.trim() || undefined,
        address: address.trim() || undefined,
        vat_number: vatNumber.trim() || undefined,
        notes: notes.trim() || undefined,
        contacts: type === 'company' ? contacts : undefined
      };

      if (mode === 'create') {
        await createAgencyClient(clientData);
        toast({
          title: 'Success',
          description: 'Client created successfully.',
        });
      } else if (mode === 'edit' && client) {
        await updateAgencyClient(client.id, {
          name: clientData.name,
          type: clientData.type,
          email: clientData.email || null,
          phone: clientData.phone || null,
          address: clientData.address || null,
          vat_number: clientData.vat_number || null,
          notes: clientData.notes || null
        });
        toast({
          title: 'Success',
          description: 'Client updated successfully.',
        });
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Error saving client:', error);
      toast({
        title: 'Error',
        description: `Failed to ${mode} client. Please try again.`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      resetForm();
      onClose();
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {mode === 'create' ? 'Add New Client' : 'Edit Client'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create' 
              ? 'Create a new client for your agency. You can add contacts for company clients.'
              : 'Update client information and manage contacts.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Basic Information Section */}
          <BasicInformation
            name={name}
            setName={setName}
            type={type}
            setType={setType}
            email={email}
            setEmail={setEmail}
            phone={phone}
            setPhone={setPhone}
            address={address}
            setAddress={setAddress}
            vatNumber={vatNumber}
            setVatNumber={setVatNumber}
            notes={notes}
            setNotes={setNotes}
          />

          {/* Contact Management Section (only for companies) */}
          <ContactManagement
            clientType={type}
            contacts={contacts}
            setContacts={setContacts}
          />
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || !name.trim()}
            className="bg-stagecloud-black hover:bg-stagecloud-purple/90 text-white"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                {mode === 'create' ? 'Creating...' : 'Updating...'}
              </>
            ) : (
              mode === 'create' ? 'Create Client' : 'Update Client'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClientModal;
