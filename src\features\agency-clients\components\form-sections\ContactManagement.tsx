import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Trash2, User, Mail, Phone, Briefcase } from 'lucide-react';
import type { CreateAgencyClientContactData } from '../../types';

interface ContactManagementProps {
  clientType: 'company' | 'person';
  contacts: CreateAgencyClientContactData[];
  setContacts: (contacts: CreateAgencyClientContactData[]) => void;
}

const ContactManagement = ({
  clientType,
  contacts,
  setContacts
}: ContactManagementProps) => {
  const [newContact, setNewContact] = useState<CreateAgencyClientContactData>({
    name: '',
    email: '',
    phone: '',
    position: '',
    is_primary: false
  });

  const addContact = () => {
    if (!newContact.name.trim()) return;

    // If this is the first contact, make it primary
    const isPrimary = contacts.length === 0 || newContact.is_primary;
    
    // If making this contact primary, remove primary from others
    const updatedContacts = isPrimary 
      ? contacts.map(c => ({ ...c, is_primary: false }))
      : contacts;

    setContacts([
      ...updatedContacts,
      { ...newContact, is_primary: isPrimary }
    ]);

    // Reset form
    setNewContact({
      name: '',
      email: '',
      phone: '',
      position: '',
      is_primary: false
    });
  };

  const removeContact = (index: number) => {
    const updatedContacts = contacts.filter((_, i) => i !== index);
    
    // If we removed the primary contact and there are others, make the first one primary
    if (contacts[index].is_primary && updatedContacts.length > 0) {
      updatedContacts[0].is_primary = true;
    }
    
    setContacts(updatedContacts);
  };

  const setPrimaryContact = (index: number) => {
    const updatedContacts = contacts.map((contact, i) => ({
      ...contact,
      is_primary: i === index
    }));
    setContacts(updatedContacts);
  };

  // Only show contact management for company clients
  if (clientType !== 'company') {
    return null;
  }

  return (
    <>
      <h3 className="text-lg font-semibold">Contact Management</h3>
      <p className="text-sm text-gray-600 mb-4">
        Add contacts for this company. You can designate one as the primary contact.
      </p>

      {/* Existing Contacts */}
      {contacts.length > 0 && (
        <div className="space-y-3 mb-4">
          {contacts.map((contact, index) => (
            <Card key={index} className="relative">
              <CardContent className="pt-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">{contact.name}</span>
                      {contact.is_primary && (
                        <Badge variant="default" className="text-xs">
                          Primary
                        </Badge>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                      {contact.email && (
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          <span>{contact.email}</span>
                        </div>
                      )}
                      {contact.phone && (
                        <div className="flex items-center gap-1">
                          <Phone className="h-3 w-3" />
                          <span>{contact.phone}</span>
                        </div>
                      )}
                      {contact.position && (
                        <div className="flex items-center gap-1">
                          <Briefcase className="h-3 w-3" />
                          <span>{contact.position}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 ml-4">
                    {!contact.is_primary && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setPrimaryContact(index)}
                        className="text-xs"
                      >
                        Make Primary
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeContact(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Add New Contact Form */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Add New Contact</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="contactName">
                Contact Name *
              </Label>
              <Input
                id="contactName"
                placeholder="Enter contact name"
                value={newContact.name}
                onChange={(e) => setNewContact({ ...newContact, name: e.target.value })}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="contactPosition">
                Position/Title
              </Label>
              <Input
                id="contactPosition"
                placeholder="e.g., Manager, CEO"
                value={newContact.position}
                onChange={(e) => setNewContact({ ...newContact, position: e.target.value })}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="contactEmail">
                Email Address
              </Label>
              <Input
                id="contactEmail"
                type="email"
                placeholder="<EMAIL>"
                value={newContact.email}
                onChange={(e) => setNewContact({ ...newContact, email: e.target.value })}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="contactPhone">
                Phone Number
              </Label>
              <Input
                id="contactPhone"
                type="tel"
                placeholder="+****************"
                value={newContact.phone}
                onChange={(e) => setNewContact({ ...newContact, phone: e.target.value })}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isPrimary"
              checked={newContact.is_primary}
              onCheckedChange={(checked) => 
                setNewContact({ ...newContact, is_primary: !!checked })
              }
            />
            <Label htmlFor="isPrimary" className="text-sm">
              Set as primary contact
            </Label>
          </div>

          <Button
            type="button"
            onClick={addContact}
            disabled={!newContact.name.trim()}
            className="w-full"
            variant="outline"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Contact
          </Button>
        </CardContent>
      </Card>
    </>
  );
};

export default ContactManagement;
