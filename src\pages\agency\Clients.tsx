import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Building2, User, Mail, Phone, MapPin, FileText } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/core';
import { getAgencyClients } from '@/features/agency-clients/api/clientService';
import type { AgencyClientWithContacts, ClientFilters } from '@/features/agency-clients/types';

const AgencyClients = () => {
  const { profile } = useAuth();
  const { toast } = useToast();

  const [clients, setClients] = useState<AgencyClientWithContacts[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<'all' | 'company' | 'person'>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalClients, setTotalClients] = useState(0);
  const [showAddClientDialog, setShowAddClientDialog] = useState(false);

  const pageSize = 10;

  // Get agency entity ID from profile
  const agencyEntityId = profile?.entities?.[0]?.id;

  const fetchClients = async () => {
    if (!agencyEntityId) return;

    try {
      setLoading(true);

      const filters: ClientFilters = {
        search: searchTerm || undefined,
        type: typeFilter
      };

      const response = await getAgencyClients(agencyEntityId, filters, currentPage, pageSize);

      setClients(response.clients);
      setTotalClients(response.total);
    } catch (error) {
      console.error('Error fetching clients:', error);
      toast({
        title: 'Error',
        description: 'Failed to load clients. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClients();
  }, [agencyEntityId, searchTerm, typeFilter, currentPage]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleTypeFilter = (type: 'all' | 'company' | 'person') => {
    setTypeFilter(type);
    setCurrentPage(1); // Reset to first page when filtering
  };

  const totalPages = Math.ceil(totalClients / pageSize);

  if (!agencyEntityId) {
    return (
      <DashboardLayout userType="agency">
        <div className="flex items-center justify-center h-64">
          <p className="text-gray-500">Unable to load agency information.</p>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout userType="agency">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <h1 className="text-2xl font-bold tracking-tight">Clients</h1>
          <Button
            onClick={() => setShowAddClientDialog(true)}
            className="bg-stagecloud-black hover:bg-stagecloud-purple/90 text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Client
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search clients..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={typeFilter === 'all' ? 'default' : 'outline'}
                  onClick={() => handleTypeFilter('all')}
                  size="sm"
                >
                  All
                </Button>
                <Button
                  variant={typeFilter === 'company' ? 'default' : 'outline'}
                  onClick={() => handleTypeFilter('company')}
                  size="sm"
                >
                  Companies
                </Button>
                <Button
                  variant={typeFilter === 'person' ? 'default' : 'outline'}
                  onClick={() => handleTypeFilter('person')}
                  size="sm"
                >
                  Individuals
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Clients List */}
        <div className="space-y-4">
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <p className="text-gray-500">Loading clients...</p>
            </div>
          ) : clients.length === 0 ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <Building2 className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No clients found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {searchTerm || typeFilter !== 'all'
                      ? 'Try adjusting your search or filters.'
                      : 'Get started by adding your first client.'
                    }
                  </p>
                  {!searchTerm && typeFilter === 'all' && (
                    <div className="mt-6">
                      <Button
                        onClick={() => setShowAddClientDialog(true)}
                        className="bg-stagecloud-black hover:bg-stagecloud-purple/90 text-white"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Add Client
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            clients.map((client) => (
              <Card key={client.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="flex items-center gap-2">
                          {client.type === 'company' ? (
                            <Building2 className="h-5 w-5 text-gray-500" />
                          ) : (
                            <User className="h-5 w-5 text-gray-500" />
                          )}
                          <h3 className="text-lg font-semibold">{client.name}</h3>
                        </div>
                        <Badge variant={client.type === 'company' ? 'default' : 'secondary'}>
                          {client.type === 'company' ? 'Company' : 'Individual'}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                        {client.email && (
                          <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            <span>{client.email}</span>
                          </div>
                        )}
                        {client.phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            <span>{client.phone}</span>
                          </div>
                        )}
                        {client.address && (
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            <span className="truncate">{client.address}</span>
                          </div>
                        )}
                        {client.vat_number && (
                          <div className="flex items-center gap-2">
                            <FileText className="h-4 w-4" />
                            <span>VAT: {client.vat_number}</span>
                          </div>
                        )}
                      </div>

                      {/* Show primary contact for companies */}
                      {client.type === 'company' && client.contacts && client.contacts.length > 0 && (
                        <div className="mt-3 pt-3 border-t">
                          <p className="text-xs text-gray-500 mb-1">Primary Contact:</p>
                          {(() => {
                            const primaryContact = client.contacts.find(c => c.is_primary) || client.contacts[0];
                            return (
                              <div className="text-sm text-gray-700">
                                <span className="font-medium">{primaryContact.name}</span>
                                {primaryContact.position && (
                                  <span className="text-gray-500"> • {primaryContact.position}</span>
                                )}
                                {primaryContact.email && (
                                  <span className="text-gray-500"> • {primaryContact.email}</span>
                                )}
                              </div>
                            );
                          })()}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalClients)} of {totalClients} clients
            </p>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <span className="text-sm text-gray-700">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* TODO: Add Client Dialog will be implemented in the next step */}
      {showAddClientDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-md w-full mx-4">
            <h2 className="text-lg font-semibold mb-4">Add Client</h2>
            <p className="text-gray-600 mb-4">Client form will be implemented in the next step.</p>
            <Button onClick={() => setShowAddClientDialog(false)}>Close</Button>
          </div>
        </div>
      )}
    </DashboardLayout>
  );
};

export default AgencyClients;
